import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/health_service/widgets/blood_sugar_form.dart';

/// 测试血糖表单的简单演示页面
class TestBloodSugarFormPage extends ConsumerWidget {
  const TestBloodSugarFormPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('血糖表单测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: BloodSugarForm(formKey: formKey),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState?.validate() ?? false) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('表单验证通过！')),
                  );
                }
              },
              child: const Text('验证表单'),
            ),
          ],
        ),
      ),
    );
  }
}
