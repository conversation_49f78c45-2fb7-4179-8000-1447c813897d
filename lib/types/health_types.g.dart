// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_types.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BloodPressureData _$BloodPressureDataFromJson(Map<String, dynamic> json) =>
    BloodPressureData(
      systolic: (json['systolic'] as num).toInt(),
      diastolic: (json['diastolic'] as num).toInt(),
      pulse: (json['pulse'] as num?)?.toInt(),
      note: json['note'] as String?,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$BloodPressureDataToJson(BloodPressureData instance) =>
    <String, dynamic>{
      'systolic': instance.systolic,
      'diastolic': instance.diastolic,
      'pulse': instance.pulse,
      'note': instance.note,
      'runtimeType': instance.$type,
    };

BloodSugarData _$BloodSugarDataFromJson(Map<String, dynamic> json) =>
    BloodSugarData(
      value: (json['value'] as num).toDouble(),
      bloodSugarType: $enumDecodeNullable(
        _$BloodSugarTypeEnumEnumMap,
        json['bloodSugarType'],
      ),
      mealTime: $enumDecodeNullable(_$MealTimeEnumEnumMap, json['mealTime']),
      note: json['note'] as String?,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$BloodSugarDataToJson(BloodSugarData instance) =>
    <String, dynamic>{
      'value': instance.value,
      'bloodSugarType': _$BloodSugarTypeEnumEnumMap[instance.bloodSugarType],
      'mealTime': _$MealTimeEnumEnumMap[instance.mealTime],
      'note': instance.note,
      'runtimeType': instance.$type,
    };

const _$BloodSugarTypeEnumEnumMap = {
  BloodSugarTypeEnum.fasting: 'fasting',
  BloodSugarTypeEnum.beforeMeal: 'beforeMeal',
  BloodSugarTypeEnum.afterMeal: 'afterMeal',
};

const _$MealTimeEnumEnumMap = {
  MealTimeEnum.breakfast: 'breakfast',
  MealTimeEnum.lunch: 'lunch',
  MealTimeEnum.dinner: 'dinner',
};
