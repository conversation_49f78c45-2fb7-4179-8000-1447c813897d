import 'package:drift/drift.dart' as drift;
import 'package:freezed_annotation/freezed_annotation.dart';

part 'health_types.freezed.dart';

part 'health_types.g.dart';

/// 血糖类型枚举
enum BloodSugarTypeEnum {
  fasting,    // 空腹
  beforeMeal, // 餐前
  afterMeal;  // 餐后

  String get displayName {
    switch (this) {
      case BloodSugarTypeEnum.fasting:
        return '空腹';
      case BloodSugarTypeEnum.beforeMeal:
        return '餐前';
      case BloodSugarTypeEnum.afterMeal:
        return '餐后';
    }
  }
}

/// 时间点枚举
enum MealTimeEnum {
  breakfast, // 早餐
  lunch,     // 午餐
  dinner;    // 晚餐

  String get displayName {
    switch (this) {
      case MealTimeEnum.breakfast:
        return '早餐';
      case MealTimeEnum.lunch:
        return '午餐';
      case MealTimeEnum.dinner:
        return '晚餐';
    }
  }
}

/// 健康记录数据联合类型
@freezed
sealed class HealthRecordData with _$HealthRecordData {
  /// 血压记录
  const factory HealthRecordData.bloodPressure({
    required int systolic,
    required int diastolic,
    int? pulse,
    String? note,
  }) = BloodPressureData;

  /// 血糖记录
  const factory HealthRecordData.bloodSugar({
    required double value,
    BloodSugarTypeEnum? bloodSugarType,
    MealTimeEnum? mealTime,
    String? note,
  }) = BloodSugarData;

  factory HealthRecordData.fromJson(Map<String, dynamic> json) =>
      _$HealthRecordDataFromJson(json);

  static final converter = drift.TypeConverter.json2<HealthRecordData>(
    fromJson: (json) => HealthRecordData.fromJson(json as Map<String, Object?>),
    toJson: (p) => p.toJson(),
  );
}

/// 健康记录类型
enum HealthRecordTypeEnum {
  bloodPressure,
  bloodSugar;

  static HealthRecordTypeEnum fromInt(int i) {
    return HealthRecordTypeEnum.values[i];
  }

  int toInt() {
    return index;
  }
}
