import 'package:easy_localization/easy_localization.dart';
import 'health_types.dart';

/// 血糖类型枚举扩展
extension BloodSugarTypeEnumExtension on BloodSugarTypeEnum {
  String get displayName {
    switch (this) {
      case BloodSugarTypeEnum.fasting:
        return 'blood_sugar_type_fasting'.tr();
      case BloodSugarTypeEnum.beforeMeal:
        return 'blood_sugar_type_before_meal'.tr();
      case BloodSugarTypeEnum.afterMeal:
        return 'blood_sugar_type_after_meal'.tr();
    }
  }
}

/// 时间点枚举扩展
extension MealTimeEnumExtension on MealTimeEnum {
  String get displayName {
    switch (this) {
      case MealTimeEnum.breakfast:
        return 'meal_time_breakfast'.tr();
      case MealTimeEnum.lunch:
        return 'meal_time_lunch'.tr();
      case MealTimeEnum.dinner:
        return 'meal_time_dinner'.tr();
      case MealTimeEnum.bedtime:
        return 'meal_time_bedtime'.tr();
    }
  }
}
