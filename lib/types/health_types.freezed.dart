// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'health_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
HealthRecordData _$HealthRecordDataFromJson(
  Map<String, dynamic> json
) {
        switch (json['runtimeType']) {
                  case 'bloodPressure':
          return BloodPressureData.fromJson(
            json
          );
                case 'bloodSugar':
          return BloodSugarData.fromJson(
            json
          );
        
          default:
            throw CheckedFromJsonException(
  json,
  'runtimeType',
  'HealthRecordData',
  'Invalid union type "${json['runtimeType']}"!'
);
        }
      
}

/// @nodoc
mixin _$HealthRecordData {

 String? get note;
/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HealthRecordDataCopyWith<HealthRecordData> get copyWith => _$HealthRecordDataCopyWithImpl<HealthRecordData>(this as HealthRecordData, _$identity);

  /// Serializes this HealthRecordData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HealthRecordData&&(identical(other.note, note) || other.note == note));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,note);

@override
String toString() {
  return 'HealthRecordData(note: $note)';
}


}

/// @nodoc
abstract mixin class $HealthRecordDataCopyWith<$Res>  {
  factory $HealthRecordDataCopyWith(HealthRecordData value, $Res Function(HealthRecordData) _then) = _$HealthRecordDataCopyWithImpl;
@useResult
$Res call({
 String? note
});




}
/// @nodoc
class _$HealthRecordDataCopyWithImpl<$Res>
    implements $HealthRecordDataCopyWith<$Res> {
  _$HealthRecordDataCopyWithImpl(this._self, this._then);

  final HealthRecordData _self;
  final $Res Function(HealthRecordData) _then;

/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? note = freezed,}) {
  return _then(_self.copyWith(
note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class BloodPressureData implements HealthRecordData {
  const BloodPressureData({required this.systolic, required this.diastolic, this.pulse, this.note, final  String? $type}): $type = $type ?? 'bloodPressure';
  factory BloodPressureData.fromJson(Map<String, dynamic> json) => _$BloodPressureDataFromJson(json);

 final  int systolic;
 final  int diastolic;
 final  int? pulse;
@override final  String? note;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodPressureDataCopyWith<BloodPressureData> get copyWith => _$BloodPressureDataCopyWithImpl<BloodPressureData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BloodPressureDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodPressureData&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse)&&(identical(other.note, note) || other.note == note));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,systolic,diastolic,pulse,note);

@override
String toString() {
  return 'HealthRecordData.bloodPressure(systolic: $systolic, diastolic: $diastolic, pulse: $pulse, note: $note)';
}


}

/// @nodoc
abstract mixin class $BloodPressureDataCopyWith<$Res> implements $HealthRecordDataCopyWith<$Res> {
  factory $BloodPressureDataCopyWith(BloodPressureData value, $Res Function(BloodPressureData) _then) = _$BloodPressureDataCopyWithImpl;
@override @useResult
$Res call({
 int systolic, int diastolic, int? pulse, String? note
});




}
/// @nodoc
class _$BloodPressureDataCopyWithImpl<$Res>
    implements $BloodPressureDataCopyWith<$Res> {
  _$BloodPressureDataCopyWithImpl(this._self, this._then);

  final BloodPressureData _self;
  final $Res Function(BloodPressureData) _then;

/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,Object? note = freezed,}) {
  return _then(BloodPressureData(
systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
@JsonSerializable()

class BloodSugarData implements HealthRecordData {
  const BloodSugarData({required this.value, this.bloodSugarType, this.mealTime, this.note, final  String? $type}): $type = $type ?? 'bloodSugar';
  factory BloodSugarData.fromJson(Map<String, dynamic> json) => _$BloodSugarDataFromJson(json);

 final  double value;
 final  BloodSugarTypeEnum? bloodSugarType;
 final  MealTimeEnum? mealTime;
@override final  String? note;

@JsonKey(name: 'runtimeType')
final String $type;


/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodSugarDataCopyWith<BloodSugarData> get copyWith => _$BloodSugarDataCopyWithImpl<BloodSugarData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BloodSugarDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodSugarData&&(identical(other.value, value) || other.value == value)&&(identical(other.bloodSugarType, bloodSugarType) || other.bloodSugarType == bloodSugarType)&&(identical(other.mealTime, mealTime) || other.mealTime == mealTime)&&(identical(other.note, note) || other.note == note));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,value,bloodSugarType,mealTime,note);

@override
String toString() {
  return 'HealthRecordData.bloodSugar(value: $value, bloodSugarType: $bloodSugarType, mealTime: $mealTime, note: $note)';
}


}

/// @nodoc
abstract mixin class $BloodSugarDataCopyWith<$Res> implements $HealthRecordDataCopyWith<$Res> {
  factory $BloodSugarDataCopyWith(BloodSugarData value, $Res Function(BloodSugarData) _then) = _$BloodSugarDataCopyWithImpl;
@override @useResult
$Res call({
 double value, BloodSugarTypeEnum? bloodSugarType, MealTimeEnum? mealTime, String? note
});




}
/// @nodoc
class _$BloodSugarDataCopyWithImpl<$Res>
    implements $BloodSugarDataCopyWith<$Res> {
  _$BloodSugarDataCopyWithImpl(this._self, this._then);

  final BloodSugarData _self;
  final $Res Function(BloodSugarData) _then;

/// Create a copy of HealthRecordData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? value = null,Object? bloodSugarType = freezed,Object? mealTime = freezed,Object? note = freezed,}) {
  return _then(BloodSugarData(
value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,bloodSugarType: freezed == bloodSugarType ? _self.bloodSugarType : bloodSugarType // ignore: cast_nullable_to_non_nullable
as BloodSugarTypeEnum?,mealTime: freezed == mealTime ? _self.mealTime : mealTime // ignore: cast_nullable_to_non_nullable
as MealTimeEnum?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
