// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$homeControllerHash() => r'f72c7814bf78fc5ddaa55877c7bd33a1bcd2935a';

/// 首页控制器
///
/// Copied from [HomeController].
@ProviderFor(HomeController)
final homeControllerProvider =
    AutoDisposeStreamNotifierProvider<
      HomeController,
      Map<HealthRecordTypeEnum, TodayHealthOverview>
    >.internal(
      HomeController.new,
      name: r'homeControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$homeControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$HomeController =
    AutoDisposeStreamNotifier<Map<HealthRecordTypeEnum, TodayHealthOverview>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
