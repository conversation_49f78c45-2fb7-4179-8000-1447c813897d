import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:health_diary/health_service/service_manager.dart';
import 'package:health_diary/repository/health_record_repository.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:health_diary/types/health_record.dart';

part 'recent_records_controller.g.dart';

/// 最近记录控制器
@riverpod
class RecentRecordsController extends _$RecentRecordsController {
  static const int pageSize = 100;
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  Stream<List<HealthRecordEntry>> build() {
    final repository = ref.watch(healthRecordRepositoryProvider);

    // 监听第一页数据的变化
    return repository.watchHealthRecordsPaginated(
      page: 1,
      pageSize: pageSize,
    ).map((records) {
      // 重置分页状态
      _currentPage = 1;
      _hasMore = records.length >= pageSize;

      // 转换为 HealthRecordEntry
      final entries = HealthServiceManager.convertToHealthRecordEntry(records);

      return entries;
    });
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (_isLoading || !_hasMore) {
      return;
    }
    debugPrint("_currentPage: $_currentPage");

    try {
      _isLoading = true;
      final nextPage = _currentPage + 1;
      final repository = ref.read(healthRecordRepositoryProvider);

      // 获取下一页数据
      final records = await repository.getHealthRecordsPaginated(
        page: nextPage,
        pageSize: pageSize,
      );

      _currentPage = nextPage;

      // 如果返回的记录数少于 pageSize，说明没有更多数据了
      if (records.length < pageSize) {
        _hasMore = false;
      }

      final newEntries = HealthServiceManager.convertToHealthRecordEntry(records);

      // 更新状态
      if (state.value != null) {
        state = AsyncValue.data([
          ...state.requireValue,
          ...newEntries
        ]);
      } else {
        state = AsyncValue.data([
          ...newEntries
        ]);
      }
    } catch (e) {
      log('Error loading more data: $e');
      // 保持当前状态不变
    } finally {
      _isLoading = false;
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    // 重置状态
    _currentPage = 1;
    _hasMore = true;

    // 重新构建 provider，这会触发 build 方法重新执行
    ref.invalidateSelf();
  }

  bool get hasMore => _hasMore;
  bool get isLoading => _isLoading;
}
