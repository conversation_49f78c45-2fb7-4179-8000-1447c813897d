import 'package:flutter/material.dart';

/// 数据项显示组件
class DataItemWidget extends StatelessWidget {
  final String label;
  final String value;
  final String unit;
  final bool isAbnormal;

  const DataItemWidget({
    super.key,
    required this.label,
    required this.value,
    required this.unit,
    this.isAbnormal = false,
  });

  @override
  Widget build(BuildContext context) {
    final valueColor = isAbnormal ? Colors.red : null;

    return Column(
      children: [
        if (label.isNotEmpty) const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.displayMedium?.copyWith(
            color: valueColor,
          ),
        ),
        Text(
          unit,
          style: Theme.of(context).textTheme.labelSmall,
        ),
      ],
    );
  }
}