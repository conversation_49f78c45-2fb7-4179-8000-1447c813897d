import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:health_diary/health_service/service_manager.dart';
import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'data_controller.g.dart';

@riverpod
class DataController extends _$DataController {
  @override
  Map<HealthRecordTypeEnum, HealthRecordData?> build() {
    return <HealthRecordTypeEnum, HealthRecordData?>{};
  }

  void updateBloodSugar({
    double? value,
    BloodSugarTypeEnum? bloodSugarType,
    MealTimeEnum? mealTime,
    String? note,
  }) {
    final old = state[HealthRecordTypeEnum.bloodSugar] as BloodSugarData?;
    state = {
      ...state,
      HealthRecordTypeEnum.bloodSugar: BloodSugarData(
        value: value ?? old?.value ?? 0,
        bloodSugarType: bloodSugarType ?? old?.bloodSugarType,
        mealTime: mealTime ?? old?.mealTime,
        note: note ?? old?.note,
      ),
    };
  }

  void updateBloodPressure({
    int? systolic,
    int? diastolic,
    int? pulse,
    String? note,
  }) {
    final old = state[HealthRecordTypeEnum.bloodPressure] as BloodPressureData?;
    state = {
      ...state,
      HealthRecordTypeEnum.bloodPressure: BloodPressureData(
        systolic: systolic ?? old?.systolic ?? 0,
        diastolic: diastolic ?? old?.diastolic ?? 0,
        pulse: pulse ?? old?.pulse,
        note: note ?? old?.note,
      ),
    };
  }

  bool parseScanData(List<int> data, HealthRecordTypeEnum recordType) {
    final service = HealthServiceManager.getService(recordType);
    final ret = service.parseScannerData(data);
    if (ret == null) {
      return false;
    }
    state = {...state, recordType: ret};

    return true;
  }

  HealthRecordData? getRecordData(HealthRecordTypeEnum recordType) {
    return state[recordType];
  }
}
