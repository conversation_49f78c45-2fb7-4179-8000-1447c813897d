// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dataControllerHash() => r'dfc019e124c78a8987c9995a5ddd81cd21d99429';

/// See also [DataController].
@ProviderFor(DataController)
final dataControllerProvider =
    AutoDisposeNotifierProvider<
      DataController,
      Map<HealthRecordTypeEnum, HealthRecordData?>
    >.internal(
      DataController.new,
      name: r'dataControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$dataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DataController =
    AutoDisposeNotifier<Map<HealthRecordTypeEnum, HealthRecordData?>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
