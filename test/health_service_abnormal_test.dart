import 'package:flutter_test/flutter_test.dart';
import 'package:health_diary/health_service/pressure_service.dart';
import 'package:health_diary/health_service/sugar_service.dart';
import 'package:health_diary/repository/database.dart';
import 'package:health_diary/types/health_types.dart';

void main() {
  group('Health Service Abnormal Detection Tests', () {
    late PressureService pressureService;
    late SugarService sugarService;

    setUp(() {
      pressureService = PressureService();
      sugarService = SugarService();
    });

    group('PressureService', () {
      test('should detect abnormal blood pressure values', () {
        // 创建异常血压数据 (收缩压 >= 130 或舒张压 >= 85)
        final abnormalRecord = HealthRecord(
          id: 1,
          type: HealthRecordTypeEnum.bloodPressure,
          data: const HealthRecordData.bloodPressure(
            systolic: 140, // 异常：>= 130
            diastolic: 90, // 异常：>= 85
            pulse: 75,
          ),
          createdAt: DateTime.now(),
        );

        final entry = pressureService.parseRecordEntry(abnormalRecord);

        // 验证收缩压标记为异常
        expect(entry.values[0].isAbnormal, true);
        // 验证舒张压标记为异常
        expect(entry.values[1].isAbnormal, true);
        // 验证脉搏正常
        expect(entry.values[2].isAbnormal, false);
      });

      test('should detect normal blood pressure values', () {
        // 创建正常血压数据
        final normalRecord = HealthRecord(
          id: 1,
          type: HealthRecordTypeEnum.bloodPressure,
          data: const HealthRecordData.bloodPressure(
            systolic: 120, // 正常：< 130
            diastolic: 80,  // 正常：< 85
            pulse: 75,      // 正常：60-100
          ),
          createdAt: DateTime.now(),
        );

        final entry = pressureService.parseRecordEntry(normalRecord);

        // 验证所有值都标记为正常
        expect(entry.values[0].isAbnormal, false);
        expect(entry.values[1].isAbnormal, false);
        expect(entry.values[2].isAbnormal, false);
      });

      test('should detect abnormal pulse values', () {
        // 创建异常脉搏数据
        final abnormalPulseRecord = HealthRecord(
          id: 1,
          type: HealthRecordTypeEnum.bloodPressure,
          data: const HealthRecordData.bloodPressure(
            systolic: 120,
            diastolic: 80,
            pulse: 110, // 异常：> 100
          ),
          createdAt: DateTime.now(),
        );

        final entry = pressureService.parseRecordEntry(abnormalPulseRecord);

        // 验证脉搏标记为异常
        expect(entry.values[2].isAbnormal, true);
      });

      test('should calculate abnormal average correctly', () {
        final records = [
          HealthRecord(
            id: 1,
            type: HealthRecordTypeEnum.bloodPressure,
            data: const HealthRecordData.bloodPressure(systolic: 140, diastolic: 90),
            createdAt: DateTime.now(),
          ),
          HealthRecord(
            id: 2,
            type: HealthRecordTypeEnum.bloodPressure,
            data: const HealthRecordData.bloodPressure(systolic: 135, diastolic: 88),
            createdAt: DateTime.now(),
          ),
        ];

        final overview = pressureService.calculateAverage(records);

        // 平均值：收缩压 137.5 -> 137, 舒张压 89
        // 两个都超过阈值，应该标记为异常
        expect(overview.isAbnormal, true);
      });
    });

    group('SugarService', () {
      test('should detect abnormal blood sugar values', () {
        // 创建异常血糖数据 (< 3.9 或 > 7.8)
        final abnormalRecord = HealthRecord(
          id: 1,
          type: HealthRecordTypeEnum.bloodSugar,
          data: const HealthRecordData.bloodSugar(
            value: 8.5, // 异常：> 7.8
          ),
          createdAt: DateTime.now(),
        );

        final entry = sugarService.parseRecordEntry(abnormalRecord);

        // 验证血糖值标记为异常
        expect(entry.values[0].isAbnormal, true);
      });

      test('should detect normal blood sugar values', () {
        // 创建正常血糖数据
        final normalRecord = HealthRecord(
          id: 1,
          type: HealthRecordTypeEnum.bloodSugar,
          data: const HealthRecordData.bloodSugar(
            value: 5.5, // 正常：3.9-7.8
          ),
          createdAt: DateTime.now(),
        );

        final entry = sugarService.parseRecordEntry(normalRecord);

        // 验证血糖值标记为正常
        expect(entry.values[0].isAbnormal, false);
      });

      test('should calculate abnormal average correctly', () {
        final records = [
          HealthRecord(
            id: 1,
            type: HealthRecordTypeEnum.bloodSugar,
            data: const HealthRecordData.bloodSugar(value: 8.0),
            createdAt: DateTime.now(),
          ),
          HealthRecord(
            id: 2,
            type: HealthRecordTypeEnum.bloodSugar,
            data: const HealthRecordData.bloodSugar(value: 8.5),
            createdAt: DateTime.now(),
          ),
        ];

        final overview = sugarService.calculateAverage(records);

        // 平均值：8.25，超过 7.8，应该标记为异常
        expect(overview.isAbnormal, true);
      });

      test('should detect low blood sugar as abnormal', () {
        final lowSugarRecord = HealthRecord(
          id: 1,
          type: HealthRecordTypeEnum.bloodSugar,
          data: const HealthRecordData.bloodSugar(
            value: 3.0, // 异常：< 3.9
          ),
          createdAt: DateTime.now(),
        );

        final entry = sugarService.parseRecordEntry(lowSugarRecord);

        // 验证低血糖标记为异常
        expect(entry.values[0].isAbnormal, true);
      });
    });
  });
}
